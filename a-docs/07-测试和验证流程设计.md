# 钉钉MCP工具测试和验证流程设计

## 1. 测试流程概述

钉钉MCP工具的测试分为四个层次：
1. **单元测试**：测试单个函数和方法
2. **集成测试**：测试API调用和数据流
3. **MCP功能测试**：测试MCP工具的完整功能
4. **端到端测试**：在Claude Desktop中的实际使用测试

## 2. 单元测试模板

### 2.1 参数验证测试
```go
func Test{ServiceName}_{MethodName}_ParameterValidation(t *testing.T) {
    client := dingtalk.NewClient(1, "test_key", "test_secret")
    service := New{ServiceName}(client)
    
    tests := []struct {
        name    string
        args    map[string]interface{}
        wantErr bool
        errMsg  string
    }{
        {
            name: "正常参数",
            args: map[string]interface{}{
                "{param1}": "{valid_value1}",
                "{param2}": {valid_value2},
            },
            wantErr: false,
        },
        {
            name: "缺少必需参数",
            args: map[string]interface{}{
                "{param2}": {valid_value2},
            },
            wantErr: true,
            errMsg:  "{param1}必须是字符串类型",
        },
        {
            name: "参数类型错误",
            args: map[string]interface{}{
                "{param1}": 123, // 应该是字符串
                "{param2}": {valid_value2},
            },
            wantErr: true,
            errMsg:  "{param1}必须是字符串类型",
        },
        {
            name: "参数值无效",
            args: map[string]interface{}{
                "{param1}": "", // 空字符串
                "{param2}": {valid_value2},
            },
            wantErr: true,
            errMsg:  "{param1}不能为空",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            req := mcp.CallToolRequest{
                Params: mcp.CallToolRequestParams{
                    Arguments: tt.args,
                },
            }
            
            result, err := service.{MethodName}(context.Background(), req)
            
            if tt.wantErr {
                assert.Error(t, err)
                if tt.errMsg != "" {
                    assert.Contains(t, err.Error(), tt.errMsg)
                }
                assert.Nil(t, result)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, result)
            }
        })
    }
}
```

### 2.2 业务逻辑测试
```go
func Test{ServiceName}_{MethodName}_BusinessLogic(t *testing.T) {
    client := dingtalk.NewClient(1, "test_key", "test_secret")
    service := New{ServiceName}(client)
    
    tests := []struct {
        name           string
        args           map[string]interface{}
        mockResponse   *response.{ResponseType}
        mockError      error
        expectedResult string
        wantErr        bool
    }{
        {
            name: "成功调用",
            args: map[string]interface{}{
                "{param1}": "{valid_value1}",
            },
            mockResponse: &response.{ResponseType}{
                Response: response.Response{
                    Code: 0,
                    Msg:  "ok",
                },
                Result: {expected_result_struct},
            },
            expectedResult: "{expected_json_string}",
            wantErr:        false,
        },
        {
            name: "API调用失败",
            args: map[string]interface{}{
                "{param1}": "{valid_value1}",
            },
            mockError: errors.New("network error"),
            wantErr:   true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 设置mock
            // ... mock设置代码 ...
            
            req := mcp.CallToolRequest{
                Params: mcp.CallToolRequestParams{
                    Arguments: tt.args,
                },
            }
            
            result, err := service.{MethodName}(context.Background(), req)
            
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.expectedResult, result.Content[0].Text)
            }
        })
    }
}
```

### 2.3 响应格式化测试
```go
func Test{ServiceName}_ResponseFormatting(t *testing.T) {
    tests := []struct {
        name     string
        input    interface{}
        expected string
        wantErr  bool
    }{
        {
            name: "简单对象",
            input: map[string]interface{}{
                "id":   "123",
                "name": "测试用户",
            },
            expected: `{"id":"123","name":"测试用户"}`,
            wantErr:  false,
        },
        {
            name: "复杂嵌套对象",
            input: map[string]interface{}{
                "user": map[string]interface{}{
                    "id":   "123",
                    "name": "测试用户",
                },
                "department": map[string]interface{}{
                    "id":   "456",
                    "name": "测试部门",
                },
            },
            expected: `{"department":{"id":"456","name":"测试部门"},"user":{"id":"123","name":"测试用户"}}`,
            wantErr:  false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            marshal, err := json.Marshal(tt.input)
            
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                assert.JSONEq(t, tt.expected, string(marshal))
            }
        })
    }
}
```

## 3. 集成测试模板

### 3.1 API调用集成测试
```go
func TestIntegration_{ServiceName}_{MethodName}(t *testing.T) {
    // 跳过集成测试（除非在CI环境中）
    if testing.Short() {
        t.Skip("跳过集成测试")
    }
    
    // 从环境变量获取测试配置
    agentID := os.Getenv("TEST_DINGTALK_AGENT_ID")
    appKey := os.Getenv("TEST_DINGTALK_APP_KEY")
    appSecret := os.Getenv("TEST_DINGTALK_APP_SECRET")
    
    if agentID == "" || appKey == "" || appSecret == "" {
        t.Skip("缺少钉钉测试配置")
    }
    
    // 创建真实客户端
    client := dingtalk.NewClient(agentID, appKey, appSecret)
    service := New{ServiceName}(client)
    
    tests := []struct {
        name string
        args map[string]interface{}
    }{
        {
            name: "真实API调用",
            args: map[string]interface{}{
                "{param1}": "{real_test_value1}",
                "{param2}": {real_test_value2},
            },
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            req := mcp.CallToolRequest{
                Params: mcp.CallToolRequestParams{
                    Arguments: tt.args,
                },
            }
            
            result, err := service.{MethodName}(context.Background(), req)
            
            assert.NoError(t, err)
            assert.NotNil(t, result)
            assert.NotEmpty(t, result.Content[0].Text)
            
            // 验证返回的JSON格式
            var jsonData interface{}
            err = json.Unmarshal([]byte(result.Content[0].Text), &jsonData)
            assert.NoError(t, err, "返回结果应该是有效的JSON")
        })
    }
}
```

### 3.2 错误场景集成测试
```go
func TestIntegration_{ServiceName}_ErrorScenarios(t *testing.T) {
    if testing.Short() {
        t.Skip("跳过集成测试")
    }
    
    client := dingtalk.NewClient("invalid", "invalid", "invalid")
    service := New{ServiceName}(client)
    
    tests := []struct {
        name        string
        args        map[string]interface{}
        expectError string
    }{
        {
            name: "无效认证",
            args: map[string]interface{}{
                "{param1}": "{valid_value1}",
            },
            expectError: "认证失败",
        },
        {
            name: "资源不存在",
            args: map[string]interface{}{
                "{param1}": "nonexistent_id",
            },
            expectError: "不存在",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            req := mcp.CallToolRequest{
                Params: mcp.CallToolRequestParams{
                    Arguments: tt.args,
                },
            }
            
            result, err := service.{MethodName}(context.Background(), req)
            
            assert.Error(t, err)
            assert.Nil(t, result)
            assert.Contains(t, err.Error(), tt.expectError)
        })
    }
}
```

## 4. MCP功能测试模板

### 4.1 工具注册测试
```go
func TestMCP_{ServiceName}_ToolRegistration(t *testing.T) {
    client := dingtalk.NewClient(1, "test_key", "test_secret")
    service := New{ServiceName}(client)
    
    server := server.NewMCPServer("test", "1.0.0")
    service.AddTools(server)
    
    // 获取注册的工具列表
    tools := server.ListTools()
    
    expectedTools := map[string]struct{}{
        "{tool_name_1}": {},
        "{tool_name_2}": {},
    }
    
    assert.Equal(t, len(expectedTools), len(tools), "工具数量应该匹配")
    
    for _, tool := range tools {
        _, exists := expectedTools[tool.Name]
        assert.True(t, exists, "工具 %s 应该被注册", tool.Name)
        
        // 验证工具描述
        assert.NotEmpty(t, tool.Description, "工具 %s 应该有描述", tool.Name)
        
        // 验证参数定义
        if tool.InputSchema != nil {
            schema := tool.InputSchema.(map[string]interface{})
            properties, ok := schema["properties"].(map[string]interface{})
            assert.True(t, ok, "工具 %s 应该有参数定义", tool.Name)
            assert.NotEmpty(t, properties, "工具 %s 应该有参数", tool.Name)
        }
    }
}
```

### 4.2 工具调用测试
```go
func TestMCP_{ServiceName}_ToolExecution(t *testing.T) {
    client := dingtalk.NewClient(1, "test_key", "test_secret")
    service := New{ServiceName}(client)
    
    server := server.NewMCPServer("test", "1.0.0")
    service.AddTools(server)
    
    tests := []struct {
        toolName string
        args     map[string]interface{}
        wantErr  bool
    }{
        {
            toolName: "{tool_name}",
            args: map[string]interface{}{
                "{param1}": "{valid_value1}",
                "{param2}": {valid_value2},
            },
            wantErr: false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.toolName, func(t *testing.T) {
            req := mcp.CallToolRequest{
                Method: "tools/call",
                Params: mcp.CallToolRequestParams{
                    Name:      tt.toolName,
                    Arguments: tt.args,
                },
            }
            
            // 模拟MCP服务器调用
            result, err := server.CallTool(context.Background(), req)
            
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, result)
                assert.NotEmpty(t, result.Content)
            }
        })
    }
}
```

## 5. 端到端测试指南

### 5.1 Claude Desktop测试步骤
```markdown
## 手动测试检查清单

### 1. 环境准备
- [ ] 编译最新版本的MCP服务器
- [ ] 更新Claude Desktop配置文件
- [ ] 重启Claude Desktop应用
- [ ] 验证MCP服务器连接状态

### 2. 工具可见性测试
- [ ] 在Claude Desktop中输入"@"查看可用工具
- [ ] 验证新工具是否出现在列表中
- [ ] 检查工具描述是否正确显示
- [ ] 验证参数提示是否完整

### 3. 功能测试
- [ ] 使用有效参数调用工具
- [ ] 验证返回结果格式正确
- [ ] 测试参数验证（输入无效参数）
- [ ] 验证错误信息是否友好
- [ ] 测试边界条件

### 4. 性能测试
- [ ] 测试工具响应时间
- [ ] 验证大数据量处理
- [ ] 测试并发调用
- [ ] 检查内存使用情况

### 5. 用户体验测试
- [ ] 验证工具描述的准确性
- [ ] 检查参数说明的清晰度
- [ ] 测试错误信息的可理解性
- [ ] 验证返回结果的可读性
```

### 5.2 自动化端到端测试
```go
func TestE2E_{ServiceName}_{ToolName}(t *testing.T) {
    // 启动MCP服务器
    server := startMCPServer(t)
    defer server.Stop()
    
    // 创建MCP客户端
    client := createMCPClient(t, server.Address())
    defer client.Close()
    
    // 测试工具调用
    response, err := client.CallTool("{tool_name}", map[string]interface{}{
        "{param1}": "{test_value1}",
        "{param2}": {test_value2},
    })
    
    assert.NoError(t, err)
    assert.NotNil(t, response)
    
    // 验证响应格式
    assert.Equal(t, "text", response.Content[0].Type)
    assert.NotEmpty(t, response.Content[0].Text)
    
    // 验证JSON格式（如果适用）
    var result interface{}
    err = json.Unmarshal([]byte(response.Content[0].Text), &result)
    assert.NoError(t, err)
}
```

## 6. 测试数据管理

### 6.1 测试数据模板
```go
// 测试数据常量
const (
    TestUserID     = "test_user_123"
    TestDeptID     = 1
    TestMessage    = "这是一条测试消息"
    TestTitle      = "测试标题"
)

// 测试数据生成器
func generateTestUser() map[string]interface{} {
    return map[string]interface{}{
        "userid":     TestUserID,
        "name":       "测试用户",
        "department": []int{TestDeptID},
        "mobile":     "13800138000",
        "email":      "<EMAIL>",
    }
}

func generateTestMessage() map[string]interface{} {
    return map[string]interface{}{
        "msgtype": "text",
        "text": map[string]interface{}{
            "content": TestMessage,
        },
    }
}
```

### 6.2 Mock数据管理
```go
// Mock响应数据
var mockResponses = map[string]*response.Response{
    "get_user_list": {
        Code: 0,
        Msg:  "ok",
        Result: map[string]interface{}{
            "userlist": []map[string]interface{}{
                generateTestUser(),
            },
        },
    },
    "send_message": {
        Code: 0,
        Msg:  "ok",
        Result: map[string]interface{}{
            "task_id": 123456,
        },
    },
}

func getMockResponse(apiName string) *response.Response {
    if resp, exists := mockResponses[apiName]; exists {
        return resp
    }
    return &response.Response{Code: 0, Msg: "ok"}
}
```

## 7. 测试执行和报告

### 7.1 测试执行脚本
```bash
#!/bin/bash
# test.sh - 测试执行脚本

echo "开始执行钉钉MCP工具测试..."

# 单元测试
echo "执行单元测试..."
go test -v -short ./internal/service/... -coverprofile=unit.out

# 集成测试
echo "执行集成测试..."
go test -v ./internal/service/... -coverprofile=integration.out

# 生成测试报告
echo "生成测试覆盖率报告..."
go tool cover -html=unit.out -o unit_coverage.html
go tool cover -html=integration.out -o integration_coverage.html

# 合并覆盖率报告
echo "合并覆盖率报告..."
gocovmerge unit.out integration.out > total.out
go tool cover -html=total.out -o total_coverage.html

echo "测试完成！查看覆盖率报告: total_coverage.html"
```

### 7.2 CI/CD集成
```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Go
      uses: actions/setup-go@v2
      with:
        go-version: 1.24
    
    - name: Run unit tests
      run: go test -v -short ./... -coverprofile=coverage.out
    
    - name: Run integration tests
      run: go test -v ./...
      env:
        TEST_DINGTALK_AGENT_ID: ${{ secrets.TEST_DINGTALK_AGENT_ID }}
        TEST_DINGTALK_APP_KEY: ${{ secrets.TEST_DINGTALK_APP_KEY }}
        TEST_DINGTALK_APP_SECRET: ${{ secrets.TEST_DINGTALK_APP_SECRET }}
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
      with:
        file: ./coverage.out
```
