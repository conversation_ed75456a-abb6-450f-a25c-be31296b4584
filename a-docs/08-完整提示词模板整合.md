# 钉钉MCP工具开发完整提示词模板

## 使用说明

本模板是一个完整的、可直接使用的提示词，用于快速开发钉钉MCP工具。只需要：
1. 将钉钉API文档提供给AI助手
2. 使用本提示词模板
3. AI助手将自动生成完整的工具代码

---

## 提示词模板

```
你是一个专业的Go语言开发者，专门负责为钉钉MCP项目开发新的工具。请根据提供的钉钉API文档，严格按照以下步骤和模板生成完整的MCP工具代码。

## 项目架构信息

### 项目结构
```
dingtalk-mcp/
├── main.go                          # 程序入口
├── internal/service/                # 服务层
│   ├── employee.go                  # 员工服务
│   ├── message.go                   # 消息服务
│   └── {new_service}.go            # 新服务（你要创建的）
├── pkg/dingtalk/                    # 钉钉客户端
│   ├── dingtakl.go                 # 主客户端
│   ├── employee.go                  # 员工API
│   ├── message.go                   # 消息API
│   ├── {new_feature}.go            # 新功能API（你要创建的）
│   ├── constant/api.go             # API常量
│   ├── response/                   # 响应结构
│   └── models/                     # 数据模型
└── a-docs/                         # 文档目录
```

### 核心组件
1. **MCP工具注册**: 使用 `mcp.NewTool()` 和 `server.AddTool()`
2. **钉钉API调用**: 统一使用 `ds.Request()` 方法
3. **错误处理**: 统一的响应结构和错误检查
4. **参数验证**: 类型安全的参数提取和业务逻辑验证

### 命名规范
- API常量: PascalCase + Key (如 `GetUserListKey`)
- 响应结构: PascalCase + Response (如 `GetUserListResponse`)
- 服务结构: PascalCase (如 `UserService`)
- MCP工具名: snake_case (如 `get_user_list`)
- 文件名: snake_case (如 `user_service.go`)

## 开发步骤

### 步骤1: API分析
请分析提供的API文档，提取以下信息：
- API名称和功能描述
- HTTP方法和端点
- 请求参数（必需/可选、类型、验证规则）
- 响应结构
- 错误码和错误信息

### 步骤2: 设计MCP工具
基于API分析，设计MCP工具：
- 工具名称（snake_case）
- 工具描述（中文）
- 参数映射（MCP参数 -> API参数）
- 响应格式（JSON/TEXT）

### 步骤3: 生成代码

#### 3.1 API常量定义
在 `pkg/dingtalk/constant/api.go` 中添加：
```go
const (
    {API_CONSTANT_NAME}Key = "{API_ENDPOINT}" // {API_DESCRIPTION}
)
```

#### 3.2 响应结构定义
创建 `pkg/dingtalk/response/{feature_name}.go`：
```go
package response

// {RESPONSE_STRUCT_NAME} {API_DESCRIPTION}响应结构
type {RESPONSE_STRUCT_NAME} struct {
    Response
    Result {RESULT_STRUCT_NAME} `json:"result"`
}

// {RESULT_STRUCT_NAME} {API_DESCRIPTION}结果数据
type {RESULT_STRUCT_NAME} struct {
    // 根据API文档定义字段
    {FIELD_NAME} {FIELD_TYPE} `json:"{JSON_TAG}"`
}
```

#### 3.3 客户端方法实现
创建 `pkg/dingtalk/{feature_name}.go`：
```go
package dingtalk

import (
    "net/http"
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk/constant"
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk/response"
)

// {CLIENT_METHOD_NAME} {API_DESCRIPTION}
func (ds *DingTalk) {CLIENT_METHOD_NAME}({PARAMETERS}) (*response.{RESPONSE_STRUCT_NAME}, error) {
    var (
        body = map[string]interface{}{
            "{API_PARAM_NAME}": {GO_PARAM_NAME},
        }
        data = &response.{RESPONSE_STRUCT_NAME}{}
        err  error
    )
    
    if err = ds.Request(http.Method{HTTP_METHOD}, constant.{API_CONSTANT_NAME}Key, nil, body, data); err != nil {
        return nil, err
    }
    
    return data, nil
}
```

#### 3.4 服务层实现
创建 `internal/service/{feature_name}.go`：
```go
package service

import (
    "context"
    "encoding/json"
    "fmt"
    "strings"
    "regexp"
    
    "github.com/mark3labs/mcp-go/mcp"
    "github.com/mark3labs/mcp-go/server"
    "github.com/pkg/errors"
    
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk"
)

type {SERVICE_STRUCT_NAME} struct {
    client *dingtalk.DingTalk
}

func New{SERVICE_STRUCT_NAME}(client *dingtalk.DingTalk) *{SERVICE_STRUCT_NAME} {
    return &{SERVICE_STRUCT_NAME}{client: client}
}

// {TOOL_METHOD_NAME} {TOOL_DESCRIPTION}
func (svc *{SERVICE_STRUCT_NAME}) {TOOL_METHOD_NAME}(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 1. 参数提取和类型验证
    {PARAMETER_EXTRACTION_CODE}
    
    // 2. 参数业务逻辑验证
    {PARAMETER_VALIDATION_CODE}
    
    // 3. 调用钉钉API
    resp, err := svc.client.{CLIENT_METHOD_NAME}({PARAMETERS})
    if err != nil {
        return nil, fmt.Errorf("调用钉钉API失败: %w", err)
    }
    
    // 4. 响应格式化
    {RESPONSE_FORMATTING_CODE}
}

// AddTools 注册MCP工具
func (svc *{SERVICE_STRUCT_NAME}) AddTools(server *server.MCPServer) {
    {TOOL_REGISTRATION_CODE}
}
```

### 步骤4: 参数处理模板

#### 4.1 参数提取（类型安全）
```go
// 必需字符串参数
{paramName}, ok := req.Params.Arguments["{mcpParamName}"].(string)
if !ok {
    return nil, errors.New("{mcpParamName}必须是字符串类型")
}

// 必需整数参数（处理JSON数字）
{paramName}, ok := req.Params.Arguments["{mcpParamName}"].(int)
if !ok {
    if floatVal, ok := req.Params.Arguments["{mcpParamName}"].(float64); ok {
        {paramName} = int(floatVal)
    } else {
        return nil, errors.New("{mcpParamName}必须是整数类型")
    }
}

// 必需布尔参数
{paramName}, ok := req.Params.Arguments["{mcpParamName}"].(bool)
if !ok {
    return nil, errors.New("{mcpParamName}必须是布尔类型")
}

// 可选参数（带默认值）
{paramName} := {defaultValue}
if val, ok := req.Params.Arguments["{mcpParamName}"].({type}); ok {
    {paramName} = val
} else if req.Params.Arguments["{mcpParamName}"] != nil {
    return nil, errors.New("{mcpParamName}必须是{type}类型")
}
```

#### 4.2 参数验证
```go
// 非空验证
if strings.TrimSpace({paramName}) == "" {
    return nil, errors.New("{mcpParamName}不能为空")
}

// 长度验证
if len({paramName}) > {maxLength} {
    return nil, errors.New("{mcpParamName}长度不能超过{maxLength}个字符")
}

// 数值范围验证
if {paramName} < {minValue} || {paramName} > {maxValue} {
    return nil, fmt.Errorf("{mcpParamName}必须在%d到%d之间", {minValue}, {maxValue})
}

// 格式验证（如邮箱、手机号等）
if !regexp.MustCompile(`{regex_pattern}`).MatchString({paramName}) {
    return nil, errors.New("{mcpParamName}格式不正确")
}

// 枚举值验证
validValues := []string{"{value1}", "{value2}"}
valid := false
for _, v := range validValues {
    if {paramName} == v {
        valid = true
        break
    }
}
if !valid {
    return nil, fmt.Errorf("{mcpParamName}必须是以下值之一: %s", strings.Join(validValues, ", "))
}
```

### 步骤5: 响应处理模板

```go
// JSON响应（复杂数据）
if marshal, err := json.Marshal(resp.Result); err != nil {
    return nil, fmt.Errorf("响应数据序列化失败: %w", err)
} else {
    return mcp.NewToolResultText(string(marshal)), nil
}

// 简单文本响应
return mcp.NewToolResultText({simpleValue}), nil

// 数字转字符串响应
return mcp.NewToolResultText(strconv.Itoa({numberValue})), nil
```

### 步骤6: 工具注册模板

```go
{toolVariableName} := mcp.NewTool("{tool_name}",
    mcp.WithDescription("{tool_description}"),
    mcp.WithString("{param_name}",
        mcp.Required(),
        mcp.Description("{param_description}")),
    mcp.WithInt("{param_name}",
        mcp.Description("{param_description}")),
    mcp.WithBoolean("{param_name}",
        mcp.Required(),
        mcp.Description("{param_description}")))

server.AddTool({toolVariableName}, svc.{ToolMethodName})
```

### 步骤7: 主程序集成

在 `main.go` 中添加：
```go
// 注册新服务
service.New{ServiceName}(client).AddTools(svc)
```

## 质量要求

1. **代码质量**
   - 所有函数和结构体必须有中文注释
   - 遵循项目的命名规范
   - 保持代码结构的一致性

2. **错误处理**
   - 提供友好的中文错误信息
   - 使用fmt.Errorf包装错误
   - 区分参数错误和API错误

3. **参数验证**
   - 必须进行类型安全检查
   - 必须进行业务逻辑验证
   - 提供清晰的验证错误信息

4. **响应处理**
   - 复杂数据使用JSON格式
   - 简单数据使用文本格式
   - 处理JSON序列化错误

## 输出要求

请按照以下顺序输出完整的代码：

1. **API常量定义** (pkg/dingtalk/constant/api.go 的新增内容)
2. **响应结构定义** (pkg/dingtalk/response/{feature}.go 完整文件)
3. **客户端方法实现** (pkg/dingtalk/{feature}.go 完整文件)
4. **服务层实现** (internal/service/{feature}.go 完整文件)
5. **主程序集成代码** (main.go 的修改内容)
6. **使用说明和测试建议**

每个代码块都要包含完整的包声明、导入语句和实现代码。

现在，请提供钉钉API文档，我将为你生成完整的MCP工具代码。
```

---

## 模板使用示例

### 示例输入
```
API文档：
- API名称：获取部门列表
- 端点：/topapi/v2/department/listsub
- 方法：POST
- 参数：
  - dept_id: 整数，必需，父部门ID
  - language: 字符串，可选，语言设置
- 响应：
  {
    "errcode": 0,
    "errmsg": "ok",
    "result": {
      "dept_list": [
        {
          "dept_id": 123,
          "name": "技术部",
          "parent_id": 1
        }
      ]
    }
  }
```

### 示例输出
AI助手将生成：
1. API常量：`ListSubDepartmentKey`
2. 响应结构：`ListSubDepartmentResponse`
3. 客户端方法：`ListSubDepartment`
4. 服务实现：`DepartmentService.ListSubDepartment`
5. MCP工具：`list_sub_department`
6. 完整的参数验证和错误处理代码

## 模板优势

1. **标准化**：确保所有工具遵循相同的代码结构和质量标准
2. **完整性**：涵盖从API分析到代码实现的完整流程
3. **可复用**：一次设计，多次使用，提高开发效率
4. **质量保证**：内置最佳实践和错误处理模式
5. **易维护**：统一的代码风格便于后续维护和扩展
